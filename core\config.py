"""
Configuration management for the multi-tenant email automation system.
Handles environment-specific settings for development vs production modes.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()


@dataclass
class KeyVaultConfig:
    """Configuration for Azure Key Vault connection."""
    vault_url: str
    tenant_id: Optional[str] = None
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    use_managed_identity: bool = False


@dataclass
class EnvironmentConfig:
    """Configuration for a specific environment (development or production)."""
    name: str
    key_vault: KeyVaultConfig
    authority: str
    scopes: list[str]
    redirect_uri: str
    use_key_vault: bool = True


class ConfigManager:
    """Manages configuration for different environments."""
    
    def __init__(self):
        self._current_env = os.getenv("MAIL_AUTO_ENVIRONMENT", "development").lower()
        self._configs = self._load_configurations()
    
    def _load_configurations(self) -> Dict[str, EnvironmentConfig]:
        """Load configuration for all environments."""
        configs = {}
        
        # Development configuration (for personal @outlook.com account)
        dev_key_vault = KeyVaultConfig(
            vault_url=os.getenv("DEV_KEY_VAULT_URL", "https://mail-auto-dev-kv.vault.azure.net/"),
            tenant_id=os.getenv("DEV_TENANT_ID"),
            client_id=os.getenv("DEV_CLIENT_ID"),
            client_secret=os.getenv("DEV_CLIENT_SECRET"),
            use_managed_identity=os.getenv("DEV_USE_MANAGED_IDENTITY", "false").lower() == "true"
        )
        
        configs["development"] = EnvironmentConfig(
            name="development",
            key_vault=dev_key_vault,
            authority="https://login.microsoftonline.com/consumers",
            scopes=[
                "Mail.ReadWrite",
                "Mail.Send",
                "Files.ReadWrite",
                "Files.ReadWrite.All",
                "offline_access"
            ],
            redirect_uri=os.getenv("DEV_REDIRECT_URI", "http://localhost:8000"),
            use_key_vault=os.getenv("DEV_USE_KEY_VAULT", "true").lower() == "true"
        )
        
        # Production configuration (for customer accounts)
        prod_key_vault = KeyVaultConfig(
            vault_url=os.getenv("PROD_KEY_VAULT_URL", "https://mail-auto-prod-kv.vault.azure.net/"),
            tenant_id=os.getenv("PROD_TENANT_ID"),
            client_id=os.getenv("PROD_CLIENT_ID"),
            client_secret=os.getenv("PROD_CLIENT_SECRET"),
            use_managed_identity=os.getenv("PROD_USE_MANAGED_IDENTITY", "true").lower() == "true"
        )
        
        configs["production"] = EnvironmentConfig(
            name="production",
            key_vault=prod_key_vault,
            authority="https://login.microsoftonline.com/common",
            scopes=[
                "Mail.ReadWrite",
                "Mail.Send", 
                "Files.ReadWrite.All",
                "offline_access"
            ],
            redirect_uri=os.getenv("PROD_REDIRECT_URI", "https://yourdomain.com/auth/callback"),
            use_key_vault=os.getenv("PROD_USE_KEY_VAULT", "true").lower() == "true"
        )
        
        return configs
    
    @property
    def current_environment(self) -> str:
        """Get the current environment name."""
        return self._current_env
    
    @property
    def config(self) -> EnvironmentConfig:
        """Get the configuration for the current environment."""
        if self._current_env not in self._configs:
            raise ValueError(f"Unknown environment: {self._current_env}")
        return self._configs[self._current_env]
    
    def get_config(self, environment: str) -> EnvironmentConfig:
        """Get configuration for a specific environment."""
        if environment not in self._configs:
            raise ValueError(f"Unknown environment: {environment}")
        return self._configs[environment]
    
    def set_environment(self, environment: str) -> None:
        """Set the current environment."""
        if environment not in self._configs:
            raise ValueError(f"Unknown environment: {environment}")
        self._current_env = environment
    
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self._current_env == "development"
    
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self._current_env == "production"


# Global configuration manager instance
config_manager = ConfigManager()
