"""End-to-end orchestrator for the multi-tenant email-to-OneDrive pipeline.

Usage (interactive):
    python main.py [--once]

If --once is supplied the script processes unread e-mails once and exits.
Otherwise it runs forever with a sleep between cycles (default 5 min).
"""
from __future__ import annotations

import argparse
import time
from datetime import datetime

from core.tenant_loader import list_tenants
from core.mail_reader import read_mail, get_tenant_mail_headers
from core.unified_file_analyzer import analyze_file_bytes

CYCLE_SECONDS = 10  # 10 seconds

def process_tenants() -> None:
    tenants = list_tenants()
    if not tenants:
        print("⚠️  No tenants configured – nothing to do.")
        return

    for tenant in tenants:
        tenant_name, _creds_path, tenant_cfg, _token_cache = tenant
        print("\n==============================")
        print(f"🏢 Tenant: {tenant_name}  |  {datetime.now().isoformat(timespec='seconds')}")
        print("==============================")

        # Fetch unread file attachments
        attachments = read_mail(tenant)
        if not attachments:
            continue

        headers = get_tenant_mail_headers(tenant)
        if not headers:
            print("❌ Could not obtain auth headers – skipping tenant.")
            continue

        for file_bytes, filename, sender, received, mail_body, mime_type in attachments:
            print(f"\n🔄 Processing attachment {filename} from {sender} ({received})")
            analyze_file_bytes(file_bytes, tenant_cfg, filename, headers, mail_body, mime_type)


def main() -> None:

    parser = argparse.ArgumentParser(description="Run the email-to-OneDrive pipeline")
    parser.add_argument("--once", action="store_true", help="Run a single cycle and exit")
    parser.add_argument(
        "--interval",
        type=int,
        default=CYCLE_SECONDS,
        help="Seconds to wait between cycles (default 300)",
    )
    args = parser.parse_args()

    if args.once:
        process_tenants()
    else:
        while True:
            process_tenants()
            print(f"\n⏳ Sleeping {args.interval}s …")
            time.sleep(args.interval)


if __name__ == "__main__":
    main()
