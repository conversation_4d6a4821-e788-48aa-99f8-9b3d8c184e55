"""
Refactored mail reader module for multi-tenant email automation system.
Handles reading emails for specific tenants with their own credentials.
Supports both Key Vault and legacy JSON file credential storage.
"""

import json
import os
import requests
import msal
import base64
import logging
from typing import List, Tuple, Dict, Any, Optional

from .config import config_manager
from .tenant_loader import get_tenant_credentials, get_tenant_token_cache, save_tenant_token_cache

logger = logging.getLogger(__name__)


def read_mail(tenant_data: Tuple[str, str, Dict[Any, Any], str]) -> List[Tuple[bytes, str, str, str, str, str]]:
    """
    Read emails for a specific tenant and return file attachments.

    Args:
        tenant_data: Tuple of (tenant_name, creds_source, config_dict, token_cache_source)
        - creds_source: Either file path or "key_vault" for Key Vault storage
        - token_cache_source: Either file path or "key_vault" for Key Vault storage

    Returns:
        List of tuples: (file_bytes, filename, sender, received_time, mail_body, mime_type)
    """
    tenant_name, creds_source, config_dict, token_cache_source = tenant_data

    # Load tenant credentials (supports both Key Vault and legacy files)
    creds = get_tenant_credentials(tenant_name)
    if not creds:
        logger.error(f"Failed to load credentials for tenant {tenant_name}")
        return []
    
    CLIENT_ID = creds["client_id"]
    REDIRECT_URI = creds["redirect_uri"]

    # Use environment-specific configuration
    env_config = config_manager.config
    AUTHORITY = env_config.authority
    SCOPE = env_config.scopes

    # Token cache handling (supports both Key Vault and legacy files)
    cache = msal.SerializableTokenCache()
    token_cache_data = get_tenant_token_cache(tenant_name)
    if token_cache_data:
        cache.deserialize(token_cache_data)
    
    app = msal.PublicClientApplication(
        client_id=CLIENT_ID,
        authority=AUTHORITY,
        token_cache=cache
    )
    
    # Try silent login
    accounts = app.get_accounts()
    result = None
    if accounts:
        result = app.acquire_token_silent(SCOPE, account=accounts[0])
    
    # Fallback: interactive login
    if not result:
        print(f"🔐 Need interactive login for tenant {tenant_name}. Use the code below:")
        flow = app.initiate_device_flow(scopes=SCOPE)
        if "user_code" not in flow:
            print(f"❌ Failed to create device flow for tenant {tenant_name}. Check app registration.")
            return []
        
        print("Go to:", flow["verification_uri"])
        print("User code:", flow["user_code"])
        result = app.acquire_token_by_device_flow(flow)
    
    # Save token cache (supports both Key Vault and legacy files)
    if result and "access_token" in result:
        save_tenant_token_cache(tenant_name, cache.serialize())
        logger.info(f"Authenticated successfully for tenant {tenant_name}")
        print(f"✅ Authenticated successfully for tenant {tenant_name}!")
        
        headers = {
            "Authorization": f"Bearer {result['access_token']}",
            "Content-Type": "application/json"
        }
        
        # Fetch unread emails with attachments
        response = requests.get(
            "https://graph.microsoft.com/v1.0/me/messages?$filter=isRead eq false&$top=10&$select=subject,from,receivedDateTime,isRead,hasAttachments",
            headers=headers
        )
        
        file_attachments = []
        
        if response.status_code == 200:
            messages = response.json().get("value", [])
            if not messages:
                print(f"📭 No unread messages found for tenant {tenant_name}.")
                return []
            
            print(f"\n📬 Processing unread emails for tenant {tenant_name}:\n")
            
            for msg in messages:
                print(f"Subject: {msg['subject']}")
                sender_name = msg['from']['emailAddress']['name']
                sender_email = msg['from']['emailAddress']['address']
                sender = f"{sender_name} <{sender_email}>"
                received_time = msg['receivedDateTime']
                
                print(f"From: {sender}")
                print(f"Received: {received_time}")
                print("-" * 60)
                
                # Fetch plain-text body preview once per message
                body_preview = ""
                try:
                    body_resp = requests.get(
                        f"https://graph.microsoft.com/v1.0/me/messages/{msg['id']}?$select=bodyPreview",
                        headers=headers,
                        timeout=10,
                    )
                    if body_resp.status_code == 200:
                        body_preview = body_resp.json().get("bodyPreview", "")
                except requests.RequestException as exc:
                    print(f"⚠️  Failed fetching body preview: {exc}")

                if msg.get("hasAttachments"):
                    attachments_url = f"https://graph.microsoft.com/v1.0/me/messages/{msg['id']}/attachments"
                    attachments_resp = requests.get(attachments_url, headers=headers)
                    
                    if attachments_resp.status_code == 200:
                       attachments = attachments_resp.json().get("value", [])
                       for att in attachments:
                           print(f"📎 Found attachment: {att['name']} ({att['contentType']})")
                           if "contentBytes" in att:
                               try:
                                   file_bytes = base64.b64decode(att["contentBytes"])
                                   file_attachments.append((
                                       file_bytes,
                                       att["name"],
                                       sender,
                                       received_time,
                                       body_preview,
                                       att["contentType"]  # Add MIME type
                                   ))
                                   print(f"✅ Extracted attachment: {att['name']}")
                               except Exception as e:
                                   print(f"❌ Failed to decode attachment {att['name']}: {e}")
                else:
                    print("📎 No attachments.")
                
                # Mark email as read
                message_id = msg["id"]
                patch_url = f"https://graph.microsoft.com/v1.0/me/messages/{message_id}"
                patch_data = json.dumps({"isRead": True})
                patch_response = requests.patch(patch_url, headers=headers, data=patch_data)
                
                if patch_response.status_code == 200:
                    print("✅ Marked as read.\n")
                else:
                    print(f"⚠️ Failed to mark as read: {patch_response.status_code}")
                    print(patch_response.text)
        else:
            print(f"❌ Failed to fetch messages for tenant {tenant_name}: {response.status_code}")
            print(response.text)
        
        return file_attachments
    else:
        print(f"❌ Authentication failed for tenant {tenant_name}.")
        if result:
            print(result.get("error_description"))
        return []


def get_tenant_mail_headers(tenant_data: Tuple[str, str, Dict[Any, Any], str]) -> Dict[str, str]:
    """
    Get authenticated headers for a tenant's mail operations.

    Args:
        tenant_data: Tuple of (tenant_name, creds_source, config_dict, token_cache_source)
        - creds_source: Either file path or "key_vault" for Key Vault storage
        - token_cache_source: Either file path or "key_vault" for Key Vault storage

    Returns:
        Dictionary containing authorization headers
    """
    tenant_name, creds_source, config_dict, token_cache_source = tenant_data

    # Load tenant credentials (supports both Key Vault and legacy files)
    creds = get_tenant_credentials(tenant_name)
    if not creds:
        logger.error(f"Failed to load credentials for tenant {tenant_name}")
        return {}
    
    CLIENT_ID = creds["client_id"]

    # Use environment-specific configuration
    env_config = config_manager.config
    AUTHORITY = env_config.authority
    SCOPE = env_config.scopes

    # Token cache handling (supports both Key Vault and legacy files)
    cache = msal.SerializableTokenCache()
    token_cache_data = get_tenant_token_cache(tenant_name)
    if token_cache_data:
        cache.deserialize(token_cache_data)
    
    app = msal.PublicClientApplication(
        client_id=CLIENT_ID,
        authority=AUTHORITY,
        token_cache=cache
    )
    
    # Try silent login
    accounts = app.get_accounts()
    result = None
    if accounts:
        result = app.acquire_token_silent(SCOPE, account=accounts[0])
    
    if result and "access_token" in result:
        return {
            "Authorization": f"Bearer {result['access_token']}",
            "Content-Type": "application/json"
        }
    else:
        print(f"❌ Failed to get headers for tenant {tenant_name}")
        return {}
