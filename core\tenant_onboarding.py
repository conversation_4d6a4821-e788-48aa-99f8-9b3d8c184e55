"""
Azure AD Multi-Tenant Customer Onboarding System
Provides smooth B2B customer consent flow for Microsoft Graph access.
Supports both Key Vault and legacy JSON file credential storage.
"""

import json
import os
import secrets
import urllib.parse
import logging
from typing import Dict, Optional
from flask import Flask, request, redirect, jsonify
import msal
import requests

from .config import config_manager
from .key_vault_service import key_vault_service

logger = logging.getLogger(__name__)

class TenantOnboardingService:
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
        self.authority = "https://login.microsoftonline.com/common"
        self.scope = [
            "Mail.ReadWrite",
            "Mail.Send", 
            "Files.ReadWrite.All",
            "offline_access"
        ]
        
    def generate_consent_url(self, customer_name: str) -> str:
        """Generate Azure AD admin consent URL for customer"""
        state = f"{customer_name}_{secrets.token_urlsafe(16)}"
        
        params = {
            "client_id": self.client_id,
            "response_type": "code",
            "redirect_uri": self.redirect_uri,
            "scope": " ".join(self.scope),
            "state": state,
            "prompt": "admin_consent",
            "response_mode": "query"
        }
        
        consent_url = f"{self.authority}/oauth2/v2.0/authorize?" + urllib.parse.urlencode(params)
        return consent_url, state
    
    def handle_consent_callback(self, code: str, state: str, tenant_id: str) -> Dict:
        """Handle OAuth callback and create tenant configuration"""
        try:
            # Extract customer name from state
            customer_name = state.split('_')[0]
            
            # Exchange code for tokens
            app = msal.ConfidentialClientApplication(
                self.client_id,
                authority=f"https://login.microsoftonline.com/{tenant_id}",
                client_credential=self.client_secret
            )
            
            result = app.acquire_token_by_authorization_code(
                code,
                scopes=self.scope,
                redirect_uri=self.redirect_uri
            )
            
            if "error" in result:
                return {"success": False, "error": result.get("error_description", "Unknown error")}
            
            # Create tenant folder for config files
            tenant_folder = f"tenants/{customer_name}"
            os.makedirs(tenant_folder, exist_ok=True)

            # Prepare credentials
            credentials = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "tenant_id": tenant_id,
                "redirect_uri": self.redirect_uri
            }

            # Save credentials (Key Vault or local file based on configuration)
            if config_manager.config.use_key_vault:
                success = key_vault_service.store_tenant_credentials(customer_name, credentials)
                if not success:
                    logger.warning(f"Failed to store credentials in Key Vault for {customer_name}, falling back to local file")
                    with open(f"{tenant_folder}/credentials.json", "w") as f:
                        json.dump(credentials, f, indent=2)
                else:
                    logger.info(f"Stored credentials for {customer_name} in Key Vault")
            else:
                with open(f"{tenant_folder}/credentials.json", "w") as f:
                    json.dump(credentials, f, indent=2)
                logger.info(f"Stored credentials for {customer_name} in local file")

            # Save initial token cache
            cache = msal.SerializableTokenCache()
            cache.deserialize(result.get("token_cache", ""))

            if config_manager.config.use_key_vault:
                success = key_vault_service.store_token_cache(customer_name, cache.serialize())
                if not success:
                    logger.warning(f"Failed to store token cache in Key Vault for {customer_name}, falling back to local file")
                    with open(f"{tenant_folder}/token_cache.json", "w") as f:
                        f.write(cache.serialize())
                else:
                    logger.info(f"Stored token cache for {customer_name} in Key Vault")
            else:
                with open(f"{tenant_folder}/token_cache.json", "w") as f:
                    f.write(cache.serialize())
                logger.info(f"Stored token cache for {customer_name} in local file")
            
            # Create default config if not exists
            if not os.path.exists(f"{tenant_folder}/config.json"):
                self._create_default_config(tenant_folder, customer_name)
            
            return {
                "success": True, 
                "tenant_name": customer_name,
                "tenant_id": tenant_id,
                "message": f"Successfully connected {customer_name} tenant"
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _create_default_config(self, tenant_folder: str, customer_name: str):
        """Create default tenant configuration"""
        default_config = {
            "tenant_name": customer_name,
            "defaults": {
                "storage": {
                    "subfolder_format": "{doc_type}/{yyyy}"
                },
                "notification": {
                    "recipients": [],
                    "email_template": "A new {doc_type} has arrived and was auto-filed.\n\n{summary}\n\nPlease review the attachment."
                },
                "classifier": {
                    "confidence_threshold": 0.7
                },
                "actions": {
                    "upload": True,
                    "notify": False
                }
            },
            "document_types": {
                "invoice": {
                    "keywords": ["invoice", "faktura", "amount due"],
                    "storage": {"subfolder_format": "Invoices/{yyyy}/{supplier}"},
                    "actions": {"upload": True, "notify": True}
                },
                "certificate": {
                    "keywords": ["certificate", "certifikat", "compliance"], 
                    "actions": {"upload": True, "notify": True}
                }
            }
        }
        
        with open(f"{tenant_folder}/config.json", "w") as f:
            json.dump(default_config, f, indent=2)


# Flask app for handling OAuth callbacks
app = Flask(__name__)

# Initialize with your Azure app registration details
# TODO: Replace these with your actual Azure app values
ONBOARDING_SERVICE = TenantOnboardingService(
    client_id="YOUR_MULTITENANT_APP_CLIENT_ID",  # From Azure Portal
    client_secret="YOUR_CLIENT_SECRET",          # From Azure Portal
    # redirect_uri="http://localhost:8000/auth/callback"  # For local testing
    redirect_uri="https://yourdomain.com/auth/callback"  # For production
)

@app.route("/onboard/<customer_name>")
def onboard_customer(customer_name: str):
    """Generate consent URL for customer"""
    consent_url, state = ONBOARDING_SERVICE.generate_consent_url(customer_name)
    
    # Store state for validation (in production, use secure storage)
    # You might want to store this in a database with expiration
    
    return redirect(consent_url)

@app.route("/auth/callback")
def auth_callback():
    """Handle OAuth callback from Azure AD"""
    code = request.args.get('code')
    state = request.args.get('state')
    tenant = request.args.get('tenant')
    error = request.args.get('error')
    
    if error:
        return jsonify({"error": error, "description": request.args.get('error_description')}), 400
    
    if not code or not state or not tenant:
        return jsonify({"error": "Missing required parameters"}), 400
    
    result = ONBOARDING_SERVICE.handle_consent_callback(code, state, tenant)
    
    if result["success"]:
        return f"""
        <h1>🎉 Successfully Connected!</h1>
        <p>Welcome {result['tenant_name']}! Your tenant is now connected to our email automation system.</p>
        <p>Tenant ID: {result['tenant_id']}</p>
        <p>You can now close this window.</p>
        """
    else:
        return f"""
        <h1>❌ Connection Failed</h1>
        <p>Error: {result['error']}</p>
        <p>Please contact support if this issue persists.</p>
        """

@app.route("/health")
def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8000, debug=True)
