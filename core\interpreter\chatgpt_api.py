"""OpenAI ChatGPT-4o integration helpers.

This module provides a single public function – ``analyze_mail_and_pdf`` – that
sends the *email body* and extracted *PDF text* to OpenAI's chat completions
endpoint and returns the model's structured JSON response as a ``dict``.

Environment variables
---------------------
OPENAI_API_KEY
    The secret API key used for authenticating with the OpenAI service.

Any networking or JSON related error is retried with exponential back-off up to
``MAX_RETRIES`` times. If all attempts fail the function returns a minimal
fallback dict so that the caller can continue processing without crashing.
"""

from __future__ import annotations

from dotenv import load_dotenv
load_dotenv()

from typing import Dict, Any, List
import os
import json
import time
import logging
import requests

__all__ = ["analyze_mail_and_pdf"]

log = logging.getLogger(__name__)

OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"
MODEL = "gpt-4o"
TIMEOUT = 30  # seconds per request
MAX_RETRIES = 3
BACKOFF_SECS = 2


_SYSTEM_PROMPT = (
    "You are an intelligent document processing assistant integrated into an "
    "email automation pipeline. You receive the TEXT BODY of an e-mail and "
    "the extracted TEXT from a PDF attachment. Analyse BOTH together to: "
    "(1) identify the high-level document type (e.g. invoice, certificate of "
    "analysis, offer, safety data sheet, purchase order, etc.), "
    "(2) extract any key/value data that would be useful for downstream systems, "
    "and (3) output a concise summary to be logged. "
    "\n\nIMPORTANT EXTRACTION RULES:\n"
    "- When extracting batch numbers, lot numbers, or serial numbers: ONLY extract "
    "the actual alphanumeric identifier that follows labels like 'Batch:', 'Lot:', "
    "'Serial:', etc. NEVER extract dates as batch/lot/serial numbers.\n"
    "- Dates should be extracted separately as date fields (e.g. 'manufacturing_date', "
    "'expiry_date', 'test_date', etc.).\n"
    "- Be precise with field names - use descriptive names like 'batch_number', "
    "'lot_number', 'invoice_number', 'po_number', etc.\n"
    "- If a field contains both letters and numbers, include the complete identifier.\n\n"
    "Return your answer STRICTLY as a JSON object with these top-level keys: doc_type, "
    "summary, extracted_fields. ``extracted_fields`` "
    "must itself be a JSON object, or an empty object if not applicable. "
    "DO NOT wrap the JSON in markdown or any prose – output ONLY valid minified JSON."
)


def _build_messages(mail_body: str, pdf_text: str) -> List[Dict[str, str]]:
    """Compose the messages list for the Chat Completions endpoint."""

    user_prompt = (
        "EMAIL BODY:\n" + mail_body.strip() + "\n\n" +
        "PDF TEXT:\n" + pdf_text.strip() + "\n"
    )

    return [
        {"role": "system", "content": _SYSTEM_PROMPT},
        {"role": "user", "content": user_prompt},
    ]


def _post_chat(messages: List[Dict[str, str]]) -> Dict[str, Any]:
    """Low-level HTTP POST helper with retries and exponential back-off."""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise RuntimeError("Environment variable OPENAI_API_KEY is not set.")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }

    payload = {
        "model": MODEL,
        "messages": messages,
        "temperature": 0.0,
        "response_format": {"type": "json_object"},
    }

    for attempt in range(1, MAX_RETRIES + 1):
        try:
            resp = requests.post(
                OPENAI_API_URL,
                headers=headers,
                json=payload,
                timeout=TIMEOUT,
            )
            resp.raise_for_status()
            data = resp.json()
            return data  # type: ignore[return-value]
        except (requests.RequestException, ValueError) as exc:
            log.warning("OpenAI request failed (attempt %s/%s): %s", attempt, MAX_RETRIES, exc)
            if attempt == MAX_RETRIES:
                raise
            time.sleep(BACKOFF_SECS ** attempt)
    # Unreachable – loop either returns or raises.


def analyze_mail_and_pdf(mail_body: str, pdf_text: str) -> Dict[str, Any]:
    """Send *mail_body* and *pdf_text* to ChatGPT-4o and return its JSON dict.

    If the request fails the function logs the error and returns a fallback
    result so that the caller can keep running without the ML component.
    """
    messages = _build_messages(mail_body, pdf_text)

    try:
        raw = _post_chat(messages)
        content = raw["choices"][0]["message"]["content"].strip()
        # Debug: log first 500 chars of the model's reply to diagnose JSON issues
        log.debug("RAW ChatGPT reply: %s", content[:500])
        # The model has been instructed to output JSON only.  Parse it.
        result: Dict[str, Any] = json.loads(content)
        return result
    except Exception as exc:  # broad catch so the pipeline never aborts here
        log.error("ChatGPT analysis failed – falling back to defaults: %s", exc)
        return {
            "doc_type": "Unknown",
            "summary": "ChatGPT analysis failed.",
            "extracted_fields": {},
        }