"""Unified file analyzer for multi-format email attachments."""

from typing import Dict, <PERSON>, <PERSON><PERSON>
import json

from core.file_processors.factory import file_processor_factory
from core.interpreter.chatgpt_api import analyze_mail_and_pdf
from core.router import resolve
from core.upload_onedrive import upload_pdf_to_onedrive
from core.notification import send_notification

__all__ = ["analyze_file_bytes"]


def _normalize_document_type(chatgpt_doc_type: str, tenant_config: Dict[str, Any]) -> str:
    """Map ChatGPT's document type output to the exact format used in config.json."""
    available_types = list(tenant_config.get("document_types", {}).keys())
    
    # First try exact match (case insensitive)
    chatgpt_lower = chatgpt_doc_type.lower()
    for doc_type in available_types:
        if doc_type.lower() == chatgpt_lower:
            return doc_type
    
    # Try with spaces converted to underscores
    chatgpt_underscore = chatgpt_lower.replace(" ", "_")
    for doc_type in available_types:
        if doc_type.lower() == chatgpt_underscore:
            return doc_type
    
    # Try partial matching
    chatgpt_words = set(chatgpt_lower.split())
    for doc_type in available_types:
        config_words = set(doc_type.lower().replace("_", " ").split())
        if chatgpt_words.issubset(config_words) or config_words.issubset(chatgpt_words):
            return doc_type
    
    return chatgpt_doc_type


def analyze_file_bytes(
    file_bytes: bytes,
    tenant_config: Dict[str, Any],
    filename: str,
    headers: Dict[str, str],
    mail_body: str,
    mime_type: str = ""
) -> Tuple[str, Dict[str, Any], str]:
    """Process any supported file type and upload it according to tenant rules.

    Args:
        file_bytes: File content as bytes.
        tenant_config: Dict from <tenant>/config.json.
        filename: Original attachment filename.
        headers: Auth headers containing a valid Graph API access token.
        mail_body: Text body of the email containing the file.
        mime_type: MIME type of the file (optional).

    Returns:
        Tuple of (doc_type, extracted_data, upload_folder).
    """
    # 1) Get appropriate file processor
    processor = file_processor_factory.get_processor(mime_type, filename)
    
    if not processor:
        print(f"❌ Unsupported file type: {filename} ({mime_type})")
        return "Unknown", {}, ""
    
    # 2) Extract text from file
    result = processor.process(file_bytes, filename)
    
    if not result.success:
        print(f"❌ Failed processing {filename}: {result.error_message}")
        return "Unknown", result.metadata, ""
    
    text = result.text
    processing_metadata = result.metadata
    
    # 3) ChatGPT analysis - always extracts data
    analysis_result = analyze_mail_and_pdf(mail_body, text)
    chatgpt_doc_type = analysis_result.get("doc_type", "Unknown")
    extracted_data = analysis_result.get("extracted_fields", {})
    summary = analysis_result.get("summary", "")
    
    # Add processing metadata to extracted data
    extracted_data["_processing_info"] = processing_metadata
    
    # 4) Normalize document type to match config.json format
    doc_type = _normalize_document_type(chatgpt_doc_type, tenant_config)
    
    if summary:
        print(f"📝 Summary: {summary}")
    print(f"📄 Document type (GPT): {chatgpt_doc_type}")
    if doc_type != chatgpt_doc_type:
        print(f"📄 Normalized to: {doc_type}")
    
    if extracted_data:
        print(f"🔑 Extracted fields: {json.dumps(extracted_data, ensure_ascii=False)}")

    # 5) Resolve routing
    upload_folder = resolve(doc_type, tenant_config)
    
    # 6) Action handling - from tenant_config
    actions = tenant_config.get("defaults", {}).get("actions", {})
    
    # Get document-specific actions if available
    doc_config = tenant_config.get("document_types", {}).get(doc_type, {})
    if not doc_config:
        print(f"ℹ️ Document type '{doc_type}' not found in config, using defaults")
        doc_actions = {}
    else:
        doc_actions = doc_config.get("actions", {})
    
    # Merge the actions, with document-specific taking precedence
    should_upload = doc_actions.get("upload", actions.get("upload", True))
    should_notify = doc_actions.get("notify", actions.get("notify", False))
    
    print(f"⚙️ Actions: upload={should_upload}, notify={should_notify}")
    
    # 7) Upload handling
    if should_upload:
        if upload_folder:
            # Note: We still use the PDF uploader for now, but it works for any binary data
            ok = upload_pdf_to_onedrive(file_bytes, filename, headers, folder=upload_folder)
            if not ok:
                print("❌ Upload failed")
        else:
            print("ℹ️ No upload folder resolved; skipping upload")

    # 8) Notification handling
    if should_notify:
        send_notification(doc_type, tenant_config, summary, file_bytes, filename, headers)

    return doc_type, extracted_data, upload_folder
