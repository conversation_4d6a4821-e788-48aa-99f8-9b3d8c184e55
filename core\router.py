"""
Routing logic for mapping document type + extracted data to upload folder and extractor.
"""

from typing import Dict, Any
from datetime import datetime


def resolve(
    doc_type: str,
    tenant_config: Dict[str, Any],
) -> str:
    """Resolve the OneDrive upload folder for a document.

    This implementation supports the *new* ``config.json`` structure suggested
    by the user which contains two key sections:

    1. ``defaults.storage`` – tenant-wide defaults
    2. ``document_types.<type>.storage`` – optional per-document-type overrides

    The final folder path is composed as::

        <root_onedrive_folder>/<subfolder_format>

    with ``subfolder_format`` processed through ``str.format`` using the
    following placeholders:

    * ``{doc_type}``   – the *raw* ``doc_type`` string provided
    * ``{yyyy}``       – current 4-digit year
    * ``{mm}``         – current 2-digit month (01-12)

    Other custom placeholders (e.g. ``{supplier}``) are ignored at this stage
    because they depend on the extraction step which happens *after* routing in
    the current pipeline. They will therefore remain literally in the returned
    string but can still be meaningful for downstream tooling.
    """
    # ------------------------------------------------------------------
    # Gather defaults + overrides
    # ------------------------------------------------------------------
    defaults: Dict[str, Any] = tenant_config.get("defaults", {})
    storage_defaults: Dict[str, Any] = defaults.get("storage", {})

    doc_rules: Dict[str, Any] = tenant_config.get("document_types", {}).get(doc_type, {})
    storage_overrides: Dict[str, Any] = doc_rules.get("storage", {})

    root = storage_overrides.get(
        "root_onedrive_folder",
        storage_defaults.get("root_onedrive_folder", ""),
    )
    subfmt = storage_overrides.get(
        "subfolder_format",
        storage_defaults.get("subfolder_format", "{doc_type}"),
    )

    # ------------------------------------------------------------------
    # Placeholder substitution
    # ------------------------------------------------------------------
    now = datetime.now()
    try:
        subfolder = subfmt.format(
            doc_type=doc_type,
            yyyy=f"{now.year:04d}",
            mm=f"{now.month:02d}",
        )
    except KeyError:
        # Unknown placeholder → keep format string as-is to avoid crashing
        subfolder = subfmt

    # Normalise separators and return
    subfolder = subfolder.strip("/\\")
    root = root.strip("/\\")
    if root and subfolder:
        return f"{root}/{subfolder}"
    return subfolder or root
