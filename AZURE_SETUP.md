# Azure Multi-Tenant Setup Guide

## 1. Create Multi-Tenant Azure App Registration

1. Go to [Azure Portal](https://portal.azure.com) → **Azure Active Directory** → **App registrations**
2. Click **New registration**
3. Configure:
   - **Name**: `YourCompany Email Automation`
   - **Supported account types**: **Accounts in any organizational directory (Any Azure AD directory - Multitenant)**
   - **Redirect URI**: `https://yourdomain.com/auth/callback`

## 2. Configure API Permissions

Add these **Application permissions** (requires admin consent):
- `Mail.ReadWrite` - Read and write mail
- `Mail.Send` - Send mail  
- `Files.ReadWrite.All` - Full access to OneDrive

## 3. Create Client Secret

1. Go to **Certificates & secrets**
2. Click **New client secret**
3. Copy the secret value immediately (you won't see it again)

## 4. Update Configuration

Update [`core/tenant_onboarding.py`](file:///c:/Users/<USER>/OneDrive - Chalmers/Skrivbordet/Mail_Auto/core/tenant_onboarding.py#L85-L90):

```python
ONBOARDING_SERVICE = TenantOnboardingService(
    client_id="YOUR_APP_CLIENT_ID_HERE",
    client_secret="YOUR_CLIENT_SECRET_HERE", 
    redirect_uri="https://yourdomain.com/auth/callback"
)
```

## 5. Customer Onboarding Flow

### For each new customer:

1. **Send them this link**: `https://yourdomain.com/onboard/CUSTOMER_NAME`
   - Replace `CUSTOMER_NAME` with their company identifier (no spaces)
   - Example: `https://yourdomain.com/onboard/acme-corp`

2. **Customer clicks link** → Redirected to Microsoft login
3. **Customer admin logs in** → Sees consent screen asking for permissions
4. **Customer grants consent** → Automatically redirected back to your app
5. **System creates tenant folder** with their credentials and config

### What happens automatically:
- Creates `tenants/CUSTOMER_NAME/` folder
- Saves `credentials.json` with their tenant ID
- Saves `token_cache.json` for API access
- Creates default `config.json` for document processing rules

## 6. Running the Onboarding Service

```bash
# Install Flask if not already installed
pip install flask

# Run the onboarding service
python core/tenant_onboarding.py
```

The service runs on `http://localhost:8000` (or your production domain).

## 7. Production Considerations

- **HTTPS Required**: Azure requires HTTPS for production redirect URIs
- **State Validation**: Store state tokens securely (database with expiration)
- **Error Handling**: Implement proper error pages and logging
- **Rate Limiting**: Add rate limiting to prevent abuse
- **Monitoring**: Log all onboarding attempts for security auditing

## 8. Customer Experience

1. You send: `"Click here to connect: https://yourdomain.com/onboard/your-company"`
2. Customer sees Microsoft login page
3. Customer admin grants permissions for your app
4. Customer sees success page: "🎉 Successfully Connected!"
5. Your system immediately has access to their emails and OneDrive

**Security**: Customers maintain full control - they can revoke access anytime via Azure AD.
