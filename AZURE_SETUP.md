# Azure Multi-Tenant Setup Guide

## 1. Create Multi-Tenant Azure App Registration

1. Go to [Azure Portal](https://portal.azure.com) → **Azure Active Directory** → **App registrations**
2. Click **New registration**
3. Configure:
   - **Name**: `YourCompany Email Automation`
   - **Supported account types**: **Accounts in any organizational directory (Any Azure AD directory - Multitenant)**
   - **Redirect URI**: `https://yourdomain.com/auth/callback`

## 2. Configure API Permissions

Add these **Application permissions** (requires admin consent):
- `Mail.ReadWrite` - Read and write mail
- `Mail.Send` - Send mail  
- `Files.ReadWrite.All` - Full access to OneDrive

## 3. Create Client Secret

1. Go to **Certificates & secrets**
2. Click **New client secret**
3. Copy the secret value immediately (you won't see it again)

## 4. Update Configuration

Update [`core/tenant_onboarding.py`](file:///c:/Users/<USER>/OneDrive - Chalmers/Skrivbordet/Mail_Auto/core/tenant_onboarding.py#L85-L90):

```python
ONBOARDING_SERVICE = TenantOnboardingService(
    client_id="YOUR_APP_CLIENT_ID_HERE",
    client_secret="YOUR_CLIENT_SECRET_HERE", 
    redirect_uri="https://yourdomain.com/auth/callback"
)
```

## 5. Customer Onboarding Flow

### For each new customer:

1. **Send them this link**: `https://yourdomain.com/onboard/CUSTOMER_NAME`
   - Replace `CUSTOMER_NAME` with their company identifier (no spaces)
   - Example: `https://yourdomain.com/onboard/acme-corp`

2. **Customer clicks link** → Redirected to Microsoft login
3. **Customer admin logs in** → Sees consent screen asking for permissions
4. **Customer grants consent** → Automatically redirected back to your app
5. **System creates tenant folder** with their credentials and config

### What happens automatically:
- Creates `tenants/CUSTOMER_NAME/` folder
- Saves `credentials.json` with their tenant ID
- Saves `token_cache.json` for API access
- Creates default `config.json` for document processing rules

## 6. Running the Onboarding Service

```bash
# Install Flask if not already installed
pip install flask

# Run the onboarding service
python core/tenant_onboarding.py
```

The service runs on `http://localhost:8000` (or your production domain).

## 7. Production Deployment and Customer Onboarding

### Production Environment Setup

1. **Deploy to Azure App Service**:
   ```bash
   # Create App Service
   az webapp create --name mail-auto-prod --resource-group your-rg --plan your-plan

   # Enable managed identity
   az webapp identity assign --name mail-auto-prod --resource-group your-rg

   # Configure environment variables
   az webapp config appsettings set --name mail-auto-prod --resource-group your-rg --settings \
     MAIL_AUTO_ENVIRONMENT=production \
     PROD_USE_MANAGED_IDENTITY=true \
     PROD_KEY_VAULT_URL=https://your-prod-kv.vault.azure.net/
   ```

2. **Configure Custom Domain** (Required for production):
   - Set up your domain: `yourdomain.com`
   - Configure HTTPS certificate (Azure provides free SSL)
   - Update redirect URI in Azure AD app to: `https://yourdomain.com/auth/callback`

3. **Start Onboarding Service**:
   ```bash
   # The onboarding service runs automatically with your main application
   # It listens on: https://yourdomain.com/onboard/{customer-name}
   ```

### Customer Onboarding Process

**Yes, it's that simple!** You just send customers a personalized link:

#### Step 1: Send Customer a Link
```
https://yourdomain.com/onboard/customer-company-name
```

**Examples**:
- `https://yourdomain.com/onboard/acme-corp`
- `https://yourdomain.com/onboard/contoso-ltd`
- `https://yourdomain.com/onboard/fabrikam-inc`

#### Step 2: Customer Experience (30 seconds)
1. **Customer clicks link** → Redirected to Microsoft login
2. **Customer admin signs in** → Sees consent screen for your app
3. **Customer grants permissions** → One-click approval
4. **Success page** → "🎉 Successfully Connected!"
5. **Done!** → Your system immediately processes their emails

#### Step 3: Automatic Backend Process
When customer grants consent, the system automatically:
- Creates secure credential storage in Azure Key Vault
- Saves their tenant ID and authentication tokens
- Sets up default email processing rules
- Starts processing their emails within minutes

### Customer Communication Template

```
Subject: Connect Your Email Automation - 30 Second Setup

Hi [Customer Name],

To activate email automation for your organization, please complete this quick setup:

🔗 Click here: https://yourdomain.com/onboard/[customer-company-name]

What happens:
✅ Sign in with your Microsoft admin account
✅ Grant permissions for our service (one-time approval)
✅ Setup complete - we'll start processing emails immediately

This is secure and you maintain full control. You can revoke access
anytime through your Microsoft admin portal.

Questions? Reply to this email.

Best regards,
[Your Name]
```

### Production Considerations

- **HTTPS Required**: Azure requires HTTPS for production redirect URIs
- **State Validation**: Store state tokens securely (database with expiration)
- **Error Handling**: Implement proper error pages and logging
- **Rate Limiting**: Add rate limiting to prevent abuse
- **Monitoring**: Log all onboarding attempts for security auditing
- **Cost Monitoring**: Set up Azure cost alerts (Key Vault costs ~$0.02/customer/month)

### Security Features

- **Tenant Isolation**: Each customer's data completely separate in Key Vault
- **Managed Identity**: No secrets stored in production environment
- **Admin Consent**: Only tenant admins can approve access
- **Revocable Access**: Customers can revoke permissions anytime
- **Audit Logging**: Full audit trail of all access and operations

## 8. Scaling and Costs

### Cost Analysis
- **Key Vault**: ~$0.02 per customer per month
- **App Service**: $73-146/month (handles thousands of customers)
- **Storage**: ~$10/month for logs and backups

### Customer Capacity
- **1,000 customers**: $101/month total infrastructure
- **10,000 customers**: $281/month total infrastructure
- **Key Vault scales automatically** with no performance impact

### Revenue Impact
If charging $20/month per customer:
- **Infrastructure cost**: 0.5% of revenue
- **Key Vault cost**: 0.1% of revenue

## 9. Quick Reference

### Customer Onboarding URLs
```
Production: https://yourdomain.com/onboard/{customer-name}
Development: http://localhost:8000/onboard/{customer-name}
```

### Environment Variables for Production
```bash
MAIL_AUTO_ENVIRONMENT=production
PROD_USE_MANAGED_IDENTITY=true
PROD_KEY_VAULT_URL=https://your-prod-kv.vault.azure.net/
```

### Key Commands
```bash
# Start onboarding service
python core/tenant_onboarding.py

# Test customer onboarding
curl https://yourdomain.com/onboard/test-customer

# Check tenant status
python migrate_to_keyvault.py --verify-only --tenant customer-name
```

### Support and Troubleshooting
- **Customer can't access link**: Check if they're using admin account
- **Permissions denied**: Verify app registration permissions
- **Onboarding fails**: Check Azure AD logs and application logs
- **Cost monitoring**: Set up Azure cost alerts at $50, $100, $200
