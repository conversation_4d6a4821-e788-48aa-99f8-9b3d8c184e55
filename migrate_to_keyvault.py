#!/usr/bin/env python3
"""
Migration script to move existing tenant credentials from JSON files to Azure Key Vault.
This script helps transition from local file storage to secure Key Vault storage.
"""

import os
import json
import logging
import argparse
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import our modules
from core.config import config_manager
from core.key_vault_service import key_vault_service
from core.tenant_loader import list_tenants


def validate_credentials(credentials: Dict[str, Any]) -> bool:
    """
    Validate that credentials contain required fields.
    
    Args:
        credentials: Dictionary containing credential data
        
    Returns:
        True if valid, False otherwise
    """
    required_fields = ["client_id", "client_secret", "tenant_id", "redirect_uri"]
    
    for field in required_fields:
        if field not in credentials:
            logger.error(f"Missing required field: {field}")
            return False
        
        if not credentials[field] or not isinstance(credentials[field], str):
            logger.error(f"Invalid value for field {field}: {credentials[field]}")
            return False
    
    return True


def migrate_tenant_credentials(tenant_name: str, tenants_dir: str = "tenants") -> bool:
    """
    Migrate a single tenant's credentials to Key Vault.
    
    Args:
        tenant_name: Name of the tenant to migrate
        tenants_dir: Directory containing tenant folders
        
    Returns:
        True if successful, False otherwise
    """
    tenant_path = os.path.join(tenants_dir, tenant_name)
    creds_path = os.path.join(tenant_path, "credentials.json")
    token_cache_path = os.path.join(tenant_path, "token_cache.json")
    
    # Check if credentials file exists
    if not os.path.exists(creds_path):
        logger.warning(f"No credentials file found for tenant {tenant_name}")
        return False
    
    try:
        # Load credentials
        with open(creds_path, 'r', encoding='utf-8') as f:
            credentials = json.load(f)
        
        # Validate credentials
        if not validate_credentials(credentials):
            logger.error(f"Invalid credentials for tenant {tenant_name}")
            return False
        
        # Store credentials in Key Vault
        logger.info(f"Migrating credentials for tenant {tenant_name}...")
        success = key_vault_service.store_tenant_credentials(tenant_name, credentials)
        
        if not success:
            logger.error(f"Failed to store credentials for tenant {tenant_name} in Key Vault")
            return False
        
        logger.info(f"✅ Successfully migrated credentials for tenant {tenant_name}")
        
        # Migrate token cache if it exists
        if os.path.exists(token_cache_path):
            try:
                with open(token_cache_path, 'r', encoding='utf-8') as f:
                    token_cache = f.read()
                
                if token_cache.strip():
                    logger.info(f"Migrating token cache for tenant {tenant_name}...")
                    cache_success = key_vault_service.store_token_cache(tenant_name, token_cache)
                    
                    if cache_success:
                        logger.info(f"✅ Successfully migrated token cache for tenant {tenant_name}")
                    else:
                        logger.warning(f"Failed to migrate token cache for tenant {tenant_name}")
                else:
                    logger.info(f"Empty token cache for tenant {tenant_name}, skipping")
                    
            except Exception as e:
                logger.warning(f"Failed to migrate token cache for tenant {tenant_name}: {e}")
        
        return True
        
    except (json.JSONDecodeError, IOError) as e:
        logger.error(f"Failed to load credentials for tenant {tenant_name}: {e}")
        return False


def verify_migration(tenant_name: str) -> bool:
    """
    Verify that a tenant's credentials were successfully migrated to Key Vault.
    
    Args:
        tenant_name: Name of the tenant to verify
        
    Returns:
        True if verification successful, False otherwise
    """
    logger.info(f"Verifying migration for tenant {tenant_name}...")
    
    # Try to retrieve credentials from Key Vault
    credentials = key_vault_service.get_tenant_credentials(tenant_name)
    if not credentials:
        logger.error(f"Failed to retrieve credentials for tenant {tenant_name} from Key Vault")
        return False
    
    # Validate retrieved credentials
    if not validate_credentials(credentials):
        logger.error(f"Retrieved credentials for tenant {tenant_name} are invalid")
        return False
    
    logger.info(f"✅ Verification successful for tenant {tenant_name}")
    return True


def backup_local_files(tenant_name: str, tenants_dir: str = "tenants") -> bool:
    """
    Create backup of local credential files before migration.
    
    Args:
        tenant_name: Name of the tenant
        tenants_dir: Directory containing tenant folders
        
    Returns:
        True if successful, False otherwise
    """
    tenant_path = os.path.join(tenants_dir, tenant_name)
    backup_dir = os.path.join(tenant_path, "backup")
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        # Backup credentials file
        creds_path = os.path.join(tenant_path, "credentials.json")
        if os.path.exists(creds_path):
            backup_creds_path = os.path.join(backup_dir, "credentials.json.backup")
            with open(creds_path, 'r') as src, open(backup_creds_path, 'w') as dst:
                dst.write(src.read())
            logger.info(f"Backed up credentials for tenant {tenant_name}")
        
        # Backup token cache file
        token_cache_path = os.path.join(tenant_path, "token_cache.json")
        if os.path.exists(token_cache_path):
            backup_cache_path = os.path.join(backup_dir, "token_cache.json.backup")
            with open(token_cache_path, 'r') as src, open(backup_cache_path, 'w') as dst:
                dst.write(src.read())
            logger.info(f"Backed up token cache for tenant {tenant_name}")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to backup files for tenant {tenant_name}: {e}")
        return False


def main():
    """Main migration function."""
    parser = argparse.ArgumentParser(description="Migrate tenant credentials to Azure Key Vault")
    parser.add_argument("--tenant", help="Migrate specific tenant only")
    parser.add_argument("--verify-only", action="store_true", help="Only verify existing migrations")
    parser.add_argument("--backup", action="store_true", help="Create backup of local files")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be migrated without doing it")
    
    args = parser.parse_args()
    
    # Check if Key Vault is configured
    if not config_manager.config.use_key_vault:
        logger.error("Key Vault is not enabled in configuration. Please set use_key_vault=true")
        return 1
    
    logger.info(f"Starting migration in {config_manager.current_environment} environment")
    logger.info(f"Key Vault URL: {config_manager.config.key_vault.vault_url}")
    
    # Get list of tenants from local files
    tenants = []
    tenants_dir = "tenants"
    
    if os.path.exists(tenants_dir):
        for tenant_name in os.listdir(tenants_dir):
            tenant_path = os.path.join(tenants_dir, tenant_name)
            if os.path.isdir(tenant_path):
                creds_path = os.path.join(tenant_path, "credentials.json")
                if os.path.exists(creds_path):
                    tenants.append(tenant_name)
    
    if not tenants:
        logger.info("No tenants found with local credential files")
        return 0
    
    # Filter to specific tenant if requested
    if args.tenant:
        if args.tenant in tenants:
            tenants = [args.tenant]
        else:
            logger.error(f"Tenant {args.tenant} not found")
            return 1
    
    logger.info(f"Found {len(tenants)} tenant(s) to process: {', '.join(tenants)}")
    
    if args.dry_run:
        logger.info("DRY RUN - No changes will be made")
        for tenant_name in tenants:
            logger.info(f"Would migrate tenant: {tenant_name}")
        return 0
    
    if args.verify_only:
        logger.info("Verification mode - checking existing migrations")
        success_count = 0
        for tenant_name in tenants:
            if verify_migration(tenant_name):
                success_count += 1
        
        logger.info(f"Verification complete: {success_count}/{len(tenants)} tenants verified successfully")
        return 0 if success_count == len(tenants) else 1
    
    # Perform migration
    success_count = 0
    
    for tenant_name in tenants:
        logger.info(f"\n--- Processing tenant: {tenant_name} ---")
        
        # Create backup if requested
        if args.backup:
            if not backup_local_files(tenant_name):
                logger.warning(f"Backup failed for tenant {tenant_name}, continuing anyway...")
        
        # Migrate credentials
        if migrate_tenant_credentials(tenant_name):
            # Verify migration
            if verify_migration(tenant_name):
                success_count += 1
                logger.info(f"✅ Migration completed successfully for tenant {tenant_name}")
            else:
                logger.error(f"❌ Migration verification failed for tenant {tenant_name}")
        else:
            logger.error(f"❌ Migration failed for tenant {tenant_name}")
    
    logger.info(f"\nMigration complete: {success_count}/{len(tenants)} tenants migrated successfully")
    
    if success_count == len(tenants):
        logger.info("🎉 All tenants migrated successfully!")
        logger.info("You can now set MAIL_AUTO_ENVIRONMENT and run the application with Key Vault")
        return 0
    else:
        logger.warning("⚠️ Some migrations failed. Please check the logs and retry.")
        return 1


if __name__ == "__main__":
    exit(main())
