# Azure Key Vault Setup Guide

This guide walks you through setting up Azure Key Vault integration for secure credential management in the Mail Auto system.

## Overview

The Mail Auto system supports two environments with secure Key Vault storage:
- **Development**: For testing with your personal @outlook.com account
- **Production**: For real customer accounts (<EMAIL>, company.com, etc.)

## Prerequisites

1. Azure subscription with appropriate permissions
2. Existing Azure AD app registrations (see AZURE_SETUP.md)
3. Python 3.10+ with required dependencies

## 1. Create Azure Key Vaults

### Development Key Vault

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Key vaults** → **Create**
3. **Basics Tab**:
   - **Subscription**: Your Azure subscription
   - **Resource Group**: Your development resource group (create new if needed)
   - **Key vault name**: `mail-auto-dev-kv` (must be globally unique)
   - **Region**: Your preferred region (choose same as your app services)
   - **Pricing tier**: Standard

4. **Access Configuration Tab**:
   - **Permission model**: Select **Vault access policy** (recommended for this setup)
   - **Access policies**: Leave empty for now (we'll configure after creation)
   - **Enable access to**:
     - ✅ **Azure Virtual Machines** (if deploying to VMs)
     - ✅ **Azure Resource Manager** (for template deployments)
     - ❌ **Azure Disk Encryption** (not needed for this use case)

5. **Networking Tab**:
   - **Connectivity method**:
     - **Development**: Select **Public endpoint (all networks)** for easier development
     - **Production**: Consider **Public endpoint (selected networks)** or **Private endpoint**
   - **Virtual networks**: Leave empty for development, configure for production if using VNet
   - **Private endpoint**: Not needed for development

6. **Tags Tab** (Optional but recommended):
   ```
   Environment: Development
   Project: MailAuto
   Owner: [Your Name/Team]
   Purpose: EmailAutomationCredentials
   CostCenter: [Your Cost Center]
   ```

7. **Review + Create**: Review settings and click **Create**

### Production Key Vault

1. Create another Key Vault with these settings:
   - **Key vault name**: `mail-auto-prod-kv` (must be globally unique)
   - **Resource Group**: Your production resource group
   - **Region**: Same region as your production services
   - **Pricing tier**: Standard (or Premium for HSM-backed keys)

2. **Access Configuration** (Production-specific):
   - **Permission model**: **Vault access policy**
   - **Enable access to**:
     - ✅ **Azure Virtual Machines** (if using VMs)
     - ✅ **Azure Resource Manager** (for deployments)
     - ❌ **Azure Disk Encryption** (not needed)

3. **Networking** (Production-specific):
   - **Connectivity method**: **Public endpoint (selected networks)** or **Private endpoint**
   - **Virtual networks**: Add your production VNet if using one
   - **Allowed IP addresses**: Add your office/deployment IPs
   - **Private endpoint**: Recommended for high-security environments

4. **Tags** (Production):
   ```
   Environment: Production
   Project: MailAuto
   Owner: [Your Name/Team]
   Purpose: EmailAutomationCredentials
   CostCenter: [Your Cost Center]
   Compliance: [Any compliance requirements]
   BackupRequired: Yes
   ```

5. **Advanced Security Settings** (Production):
   - **Soft delete**: ✅ Enabled (90 days retention recommended)
   - **Purge protection**: ✅ Enabled (prevents permanent deletion)
   - **Firewall**: Configure to allow only necessary IP ranges

## 2. Configure Access Policies and Security

### Development Key Vault Access

1. Go to your development Key Vault
2. Navigate to **Access policies**
3. Click **Add Access Policy**
4. Configure permissions:
   - **Configure from template**: Select **Secret Management**
   - **Secret permissions**:
     - ✅ **Get** (retrieve secrets)
     - ✅ **List** (list secret names)
     - ✅ **Set** (create/update secrets)
     - ✅ **Delete** (remove secrets)
     - ❌ **Backup/Restore** (not needed for this use case)
     - ❌ **Recover/Purge** (not needed for development)
5. **Select principal**:
   - Search for your development app registration name
   - Select the app registration you created for development
6. **Authorized application**: Leave as **None**
7. Click **Add** then **Save**

### Production Key Vault Access

For production, you have multiple options depending on your deployment:

#### Option A: Service Principal (Development/Testing)
Same as development setup above, but use your production app registration.

#### Option B: Managed Identity (Recommended for Production)

**For Azure App Service:**
1. Go to your App Service → **Identity**
2. **System assigned**: Turn **Status** to **On**
3. Copy the **Object ID** that appears
4. Go to Key Vault → **Access policies** → **Add Access Policy**
5. Configure:
   - **Secret permissions**: Get, List, Set, Delete
   - **Select principal**: Paste the Object ID or search by App Service name
   - **Authorized application**: None
6. Click **Add** then **Save**

**For Azure Container Instances:**
1. Enable managed identity when creating the container group
2. Note the principal ID from the container group's identity
3. Add access policy in Key Vault using this principal ID

#### Option C: Azure RBAC (Alternative to Access Policies)
1. Go to Key Vault → **Access configuration**
2. Change **Permission model** to **Azure role-based access control**
3. Go to **Access control (IAM)** → **Add role assignment**
4. Assign **Key Vault Secrets User** role to your app registration or managed identity

### Network Security Configuration

#### Development Environment
For development, you can use open network access, but consider these security measures:

1. **Firewall Settings**:
   - Go to Key Vault → **Networking**
   - **Firewalls and virtual networks**:
     - Select **Selected networks**
     - **Virtual networks**: Add your development VNet (if using one)
     - **IP networks**: Add your development machine's public IP
     - ✅ **Allow trusted Microsoft services to bypass this firewall**

#### Production Environment
For production, implement strict network controls:

1. **Private Endpoint (Recommended)**:
   - Go to Key Vault → **Networking** → **Private endpoint connections**
   - Click **Create private endpoint**
   - Configure:
     - **Resource group**: Same as Key Vault
     - **Name**: `mail-auto-kv-pe`
     - **Target sub-resource**: vault
     - **Virtual network**: Your production VNet
     - **Subnet**: Dedicated subnet for private endpoints
     - **Private DNS integration**: ✅ Yes

2. **Firewall Configuration**:
   - **Public network access**: **Disable** (if using private endpoint)
   - OR **Selected networks** with specific IP ranges:
     - Add your production server IPs
     - Add your deployment pipeline IPs
     - Add your monitoring system IPs
   - ✅ **Allow trusted Microsoft services to bypass this firewall**

3. **Service Endpoints** (Alternative to Private Endpoint):
   - In your VNet subnet, enable **Microsoft.KeyVault** service endpoint
   - In Key Vault networking, add the VNet/subnet

### Advanced Security Settings

#### Soft Delete and Purge Protection
1. Go to Key Vault → **Properties**
2. **Soft-delete**: Should be enabled by default (90 days retention)
3. **Purge protection**: ✅ Enable for production (prevents permanent deletion)

#### Diagnostic Logging
1. Go to Key Vault → **Diagnostic settings**
2. Click **Add diagnostic setting**
3. Configure:
   - **Name**: `mail-auto-kv-logs`
   - **Logs**: ✅ **AuditEvent**
   - **Metrics**: ✅ **AllMetrics**
   - **Destination**:
     - ✅ **Send to Log Analytics workspace** (recommended)
     - OR **Archive to storage account**
4. **Save**

#### Monitoring and Alerts
1. **Create Alert Rules**:
   - Go to Key Vault → **Alerts** → **New alert rule**
   - Set up alerts for:
     - Failed authentication attempts
     - Unusual access patterns
     - Secret access from unexpected IPs
     - High volume of secret retrievals

2. **Key Vault Insights**:
   - Go to Key Vault → **Insights**
   - Monitor performance, availability, and usage patterns

## 3. Environment Configuration

### Install Dependencies

```bash
pip install -r requirements.txt
```

### Create Environment File

Copy `.env.example` to `.env`:

```bash
cp .env.example .env
```

### Configure .env File

Edit `.env` with your values:

```bash
# Environment setting: "development" or "production"
MAIL_AUTO_ENVIRONMENT=development

# Development Environment Settings
DEV_KEY_VAULT_URL=https://mail-auto-dev-kv.vault.azure.net/
DEV_TENANT_ID=your-azure-tenant-id
DEV_CLIENT_ID=your-dev-app-client-id
DEV_CLIENT_SECRET=your-dev-app-client-secret
DEV_REDIRECT_URI=http://localhost:8000
DEV_USE_KEY_VAULT=true
DEV_USE_MANAGED_IDENTITY=false

# Production Environment Settings
PROD_KEY_VAULT_URL=https://mail-auto-prod-kv.vault.azure.net/
PROD_TENANT_ID=your-azure-tenant-id
PROD_CLIENT_ID=your-prod-app-client-id
PROD_CLIENT_SECRET=your-prod-app-client-secret
PROD_REDIRECT_URI=https://yourdomain.com/auth/callback
PROD_USE_KEY_VAULT=true
PROD_USE_MANAGED_IDENTITY=true
```

### Important Notes

- **Never commit .env files** - Add `.env` to your `.gitignore`
- **Key Vault URLs** must end with `.vault.azure.net/`
- **Tenant ID** is your Azure AD tenant ID (same for both environments)
- **Client IDs and Secrets** are different for dev and prod app registrations

## 4. Migration from Legacy Setup

If you have existing credentials in JSON files, use the migration script:

### Dry Run (Recommended First)

```bash
python migrate_to_keyvault.py --dry-run
```

### Backup and Migrate All Tenants

```bash
python migrate_to_keyvault.py --backup
```

### Migrate Specific Tenant

```bash
python migrate_to_keyvault.py --tenant prototype --backup
```

### Verify Migration

```bash
python migrate_to_keyvault.py --verify-only
```

## 5. Testing the Setup

### Test Development Environment

```bash
MAIL_AUTO_ENVIRONMENT=development python main.py --once
```

### Test Production Environment

```bash
MAIL_AUTO_ENVIRONMENT=production python main.py --once
```

### Test Key Vault Connection

```python
# Test script
from core.config import config_manager
from core.key_vault_service import key_vault_service

print(f"Environment: {config_manager.current_environment}")
print(f"Key Vault URL: {config_manager.config.key_vault.vault_url}")

# Test connection
try:
    tenants = key_vault_service.list_tenant_secrets()
    print(f"Found {len(tenants)} tenants in Key Vault")
except Exception as e:
    print(f"Key Vault connection failed: {e}")
```

## 6. Security Best Practices

### Access Control
- Use **principle of least privilege** for Key Vault access
- Separate development and production environments completely
- Use **Managed Identity** in production to eliminate secrets

### Secret Management
- **Rotate secrets regularly** - Set up automatic rotation
- **Monitor access** - Enable Key Vault logging
- **Use separate Key Vaults** for different environments
- **Backup Key Vault** - Enable soft delete and purge protection

### Environment Isolation
- **Never use production credentials in development**
- **Use different Azure AD tenants** for dev/prod if possible
- **Implement proper CI/CD** with environment-specific deployments

## 7. Deployment to Azure

### Azure App Service

1. **Enable Managed Identity**:
   ```bash
   az webapp identity assign --name your-app --resource-group your-rg
   ```

2. **Configure App Settings**:
   ```bash
   az webapp config appsettings set --name your-app --resource-group your-rg --settings \
     MAIL_AUTO_ENVIRONMENT=production \
     PROD_KEY_VAULT_URL=https://mail-auto-prod-kv.vault.azure.net/ \
     PROD_USE_MANAGED_IDENTITY=true
   ```

3. **Grant Key Vault Access**:
   - Get the managed identity principal ID
   - Add it to Key Vault access policies

### Azure Container Instances

1. **Enable Managed Identity** in the container group
2. **Set environment variables** for production configuration
3. **Grant Key Vault access** to the managed identity

## 8. Troubleshooting

### Key Vault Access Issues

**Error**: `azure.core.exceptions.ClientAuthenticationError`

**Solutions**:
1. Check app registration permissions
2. Verify client ID and secret are correct
3. Ensure Key Vault URL is correct
4. Check access policies in Key Vault

### Environment Issues

**Error**: `Unknown environment: development`

**Solutions**:
1. Check `MAIL_AUTO_ENVIRONMENT` variable
2. Verify .env file is in the correct location
3. Ensure all required environment variables are set

### Migration Issues

**Error**: `Failed to store credentials in Key Vault`

**Solutions**:
1. Test Key Vault connection manually
2. Check access policies and permissions
3. Verify network connectivity to Key Vault
4. Check if Key Vault name is correct

### Authentication Issues

**Error**: `Failed to get headers for tenant`

**Solutions**:
1. Check if credentials exist in Key Vault
2. Verify token cache is valid
3. Clear token cache and re-authenticate
4. Check API permissions in Azure AD

### Networking Issues

**Error**: `azure.core.exceptions.ServiceRequestError: Connection timeout`

**Solutions**:
1. **Check Firewall Settings**:
   - Verify your IP is in the allowed list
   - Ensure "Allow trusted Microsoft services" is enabled
   - Check if VNet service endpoints are configured correctly

2. **Private Endpoint Issues**:
   - Verify private DNS zone is configured
   - Check if private endpoint is in "Approved" state
   - Ensure subnet has correct route table

3. **Network Security Group (NSG) Rules**:
   - Allow outbound HTTPS (443) to Key Vault
   - Check if NSG is blocking Key Vault traffic

**Error**: `Name resolution failed for vault URL`

**Solutions**:
1. **DNS Configuration**:
   - For private endpoints: Check private DNS zone integration
   - For public endpoints: Verify internet connectivity
   - Test DNS resolution: `nslookup your-keyvault.vault.azure.net`

2. **Private Endpoint DNS**:
   - Ensure private DNS zone `privatelink.vaultcore.azure.net` exists
   - Verify A record points to private endpoint IP
   - Check virtual network link to private DNS zone

### Performance Issues

**Error**: Slow Key Vault responses

**Solutions**:
1. **Regional Proximity**: Ensure Key Vault and app are in same region
2. **Connection Pooling**: Use connection pooling in your application
3. **Caching**: Implement local caching for frequently accessed secrets
4. **Rate Limiting**: Check if you're hitting Key Vault rate limits

### Access Policy Troubleshooting

**Error**: `Forbidden: The user, group or application does not have secrets get permission`

**Solutions**:
1. **Verify Access Policy**:
   - Check if correct principal is assigned
   - Verify required permissions are granted
   - Ensure access policy is saved

2. **Principal ID Issues**:
   - For App Registration: Use Application (client) ID
   - For Managed Identity: Use Principal ID (not Client ID)
   - For User: Use Object ID from Azure AD

3. **Permission Propagation**:
   - Wait 5-10 minutes for permissions to propagate
   - Try refreshing authentication tokens

## 9. Monitoring and Logging

### Enable Key Vault Logging

1. Go to Key Vault → **Diagnostic settings**
2. Add diagnostic setting:
   - **Logs**: AuditEvent
   - **Destination**: Log Analytics workspace or Storage account

### Application Logging

The system logs Key Vault operations at INFO level:
- Credential retrieval attempts
- Token cache operations
- Migration activities
- Authentication events

### Monitoring Checklist

- [ ] Key Vault access logs enabled
- [ ] Application logging configured
- [ ] Secret expiration monitoring
- [ ] Failed authentication alerts
- [ ] Unusual access pattern detection

## 10. Networking Best Practices and Checklist

### Development Environment Checklist
- [ ] Key Vault created with public endpoint access
- [ ] Your development machine IP added to firewall rules
- [ ] "Allow trusted Microsoft services" enabled
- [ ] App registration has correct access policies
- [ ] DNS resolution working (`nslookup your-keyvault.vault.azure.net`)
- [ ] Test connection with `setup_development.py`

### Production Environment Checklist
- [ ] **Network Security**:
  - [ ] Private endpoint configured (recommended)
  - [ ] OR Public endpoint with restricted IP ranges
  - [ ] Network Security Groups allow HTTPS (443) outbound
  - [ ] Service endpoints enabled on VNet subnets (if not using private endpoint)

- [ ] **DNS Configuration**:
  - [ ] Private DNS zone created (`privatelink.vaultcore.azure.net`)
  - [ ] Virtual network linked to private DNS zone
  - [ ] A record created for Key Vault private endpoint
  - [ ] DNS resolution tested from application subnet

- [ ] **Access Control**:
  - [ ] Managed identity enabled on Azure resources
  - [ ] Access policies configured for managed identity
  - [ ] Principle of least privilege applied
  - [ ] Service principal access removed (if using managed identity)

- [ ] **Security Features**:
  - [ ] Soft delete enabled (90 days retention)
  - [ ] Purge protection enabled
  - [ ] Diagnostic logging configured
  - [ ] Monitoring alerts set up
  - [ ] Regular access reviews scheduled

### Network Architecture Examples

#### Simple Development Setup
```
Internet → Your Dev Machine → Azure Key Vault (Public Endpoint)
                           ↑
                    Firewall allows your IP
```

#### Production with Private Endpoint
```
Internet → Azure App Service → VNet → Private Endpoint → Key Vault
                              ↑                        ↑
                        Service Endpoint         Private DNS Zone
                                                (privatelink.vaultcore.azure.net)
```

#### Production with VNet Integration
```
On-Premises → VPN/ExpressRoute → Azure VNet → Key Vault (Service Endpoint)
                                    ↑              ↑
                              App Service      Firewall allows
                              (VNet Integration)  VNet traffic only
```

### Common Network Configurations

#### Configuration 1: Development (Open Access)
```bash
# Key Vault Networking Settings
Public network access: All networks
Firewall: Disabled
Private endpoint: None
```

#### Configuration 2: Production (Private Endpoint)
```bash
# Key Vault Networking Settings
Public network access: Disabled
Private endpoint: Enabled
Private DNS integration: Yes
Virtual network: Production VNet
Subnet: Private endpoint subnet
```

#### Configuration 3: Production (Restricted Public)
```bash
# Key Vault Networking Settings
Public network access: Selected networks
Virtual networks: Production VNet (with service endpoint)
IP networks:
  - Production server IPs
  - Deployment pipeline IPs
  - Monitoring system IPs
Allow trusted Microsoft services: Yes
```

### Testing Network Connectivity

#### From Development Machine
```bash
# Test DNS resolution
nslookup your-keyvault.vault.azure.net

# Test HTTPS connectivity
curl -I https://your-keyvault.vault.azure.net

# Test with Azure CLI
az keyvault secret list --vault-name your-keyvault
```

#### From Azure Resource
```bash
# Test from App Service console
curl -I https://your-keyvault.vault.azure.net

# Check managed identity token
curl -H "Metadata: true" "http://***************/metadata/identity/oauth2/token?api-version=2018-02-01&resource=https://vault.azure.net"
```

## 11. Next Steps

After completing Key Vault setup:

1. **Test thoroughly** in development environment
2. **Migrate existing tenants** using the migration script
3. **Deploy to production** with managed identity
4. **Monitor and maintain** the system
5. **Set up backup and disaster recovery** procedures
6. **Implement network security monitoring**
7. **Schedule regular security reviews**

For customer onboarding with Key Vault, see the updated tenant onboarding documentation.
