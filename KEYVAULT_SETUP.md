# Azure Key Vault Setup Guide

This guide walks you through setting up Azure Key Vault integration for secure credential management in the Mail Auto system.

## Overview

The Mail Auto system supports two environments with secure Key Vault storage:
- **Development**: For testing with your personal @outlook.com account
- **Production**: For real customer accounts (<EMAIL>, company.com, etc.)

## Prerequisites

1. Azure subscription with appropriate permissions
2. Existing Azure AD app registrations (see AZURE_SETUP.md)
3. Python 3.10+ with required dependencies

## 1. Create Azure Key Vaults

### Development Key Vault

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Key vaults** → **Create**
3. Configure:
   - **Subscription**: Your Azure subscription
   - **Resource Group**: Your development resource group
   - **Key vault name**: `mail-auto-dev-kv` (must be globally unique)
   - **Region**: Your preferred region
   - **Pricing tier**: Standard

### Production Key Vault

1. Create another Key Vault:
   - **Key vault name**: `mail-auto-prod-kv` (must be globally unique)
   - **Resource Group**: Your production resource group
   - **Region**: Your preferred region
   - **Pricing tier**: Standard (or Premium for HSM)

## 2. Configure Access Policies

### Development Key Vault Access

1. Go to your development Key Vault
2. Navigate to **Access policies**
3. Click **Add Access Policy**
4. Configure:
   - **Secret permissions**: Get, List, Set, Delete
   - **Select principal**: Choose your development app registration
   - **Authorized application**: None
5. Click **Add** then **Save**

### Production Key Vault Access

For production, you have two options:

#### Option A: Service Principal (Development/Testing)
Same as development setup above, but use your production app registration.

#### Option B: Managed Identity (Recommended for Production)
1. When deploying to Azure (App Service, Container Instance, etc.)
2. Enable **System-assigned managed identity** on your Azure resource
3. In Key Vault **Access policies**:
   - **Select principal**: Choose the managed identity of your Azure resource
   - **Secret permissions**: Get, List, Set, Delete

## 3. Environment Configuration

### Install Dependencies

```bash
pip install -r requirements.txt
```

### Create Environment File

Copy `.env.example` to `.env`:

```bash
cp .env.example .env
```

### Configure .env File

Edit `.env` with your values:

```bash
# Environment setting: "development" or "production"
MAIL_AUTO_ENVIRONMENT=development

# Development Environment Settings
DEV_KEY_VAULT_URL=https://mail-auto-dev-kv.vault.azure.net/
DEV_TENANT_ID=your-azure-tenant-id
DEV_CLIENT_ID=your-dev-app-client-id
DEV_CLIENT_SECRET=your-dev-app-client-secret
DEV_REDIRECT_URI=http://localhost:8000
DEV_USE_KEY_VAULT=true
DEV_USE_MANAGED_IDENTITY=false

# Production Environment Settings
PROD_KEY_VAULT_URL=https://mail-auto-prod-kv.vault.azure.net/
PROD_TENANT_ID=your-azure-tenant-id
PROD_CLIENT_ID=your-prod-app-client-id
PROD_CLIENT_SECRET=your-prod-app-client-secret
PROD_REDIRECT_URI=https://yourdomain.com/auth/callback
PROD_USE_KEY_VAULT=true
PROD_USE_MANAGED_IDENTITY=true
```

### Important Notes

- **Never commit .env files** - Add `.env` to your `.gitignore`
- **Key Vault URLs** must end with `.vault.azure.net/`
- **Tenant ID** is your Azure AD tenant ID (same for both environments)
- **Client IDs and Secrets** are different for dev and prod app registrations

## 4. Migration from Legacy Setup

If you have existing credentials in JSON files, use the migration script:

### Dry Run (Recommended First)

```bash
python migrate_to_keyvault.py --dry-run
```

### Backup and Migrate All Tenants

```bash
python migrate_to_keyvault.py --backup
```

### Migrate Specific Tenant

```bash
python migrate_to_keyvault.py --tenant prototype --backup
```

### Verify Migration

```bash
python migrate_to_keyvault.py --verify-only
```

## 5. Testing the Setup

### Test Development Environment

```bash
MAIL_AUTO_ENVIRONMENT=development python main.py --once
```

### Test Production Environment

```bash
MAIL_AUTO_ENVIRONMENT=production python main.py --once
```

### Test Key Vault Connection

```python
# Test script
from core.config import config_manager
from core.key_vault_service import key_vault_service

print(f"Environment: {config_manager.current_environment}")
print(f"Key Vault URL: {config_manager.config.key_vault.vault_url}")

# Test connection
try:
    tenants = key_vault_service.list_tenant_secrets()
    print(f"Found {len(tenants)} tenants in Key Vault")
except Exception as e:
    print(f"Key Vault connection failed: {e}")
```

## 6. Security Best Practices

### Access Control
- Use **principle of least privilege** for Key Vault access
- Separate development and production environments completely
- Use **Managed Identity** in production to eliminate secrets

### Secret Management
- **Rotate secrets regularly** - Set up automatic rotation
- **Monitor access** - Enable Key Vault logging
- **Use separate Key Vaults** for different environments
- **Backup Key Vault** - Enable soft delete and purge protection

### Environment Isolation
- **Never use production credentials in development**
- **Use different Azure AD tenants** for dev/prod if possible
- **Implement proper CI/CD** with environment-specific deployments

## 7. Deployment to Azure

### Azure App Service

1. **Enable Managed Identity**:
   ```bash
   az webapp identity assign --name your-app --resource-group your-rg
   ```

2. **Configure App Settings**:
   ```bash
   az webapp config appsettings set --name your-app --resource-group your-rg --settings \
     MAIL_AUTO_ENVIRONMENT=production \
     PROD_KEY_VAULT_URL=https://mail-auto-prod-kv.vault.azure.net/ \
     PROD_USE_MANAGED_IDENTITY=true
   ```

3. **Grant Key Vault Access**:
   - Get the managed identity principal ID
   - Add it to Key Vault access policies

### Azure Container Instances

1. **Enable Managed Identity** in the container group
2. **Set environment variables** for production configuration
3. **Grant Key Vault access** to the managed identity

## 8. Troubleshooting

### Key Vault Access Issues

**Error**: `azure.core.exceptions.ClientAuthenticationError`

**Solutions**:
1. Check app registration permissions
2. Verify client ID and secret are correct
3. Ensure Key Vault URL is correct
4. Check access policies in Key Vault

### Environment Issues

**Error**: `Unknown environment: development`

**Solutions**:
1. Check `MAIL_AUTO_ENVIRONMENT` variable
2. Verify .env file is in the correct location
3. Ensure all required environment variables are set

### Migration Issues

**Error**: `Failed to store credentials in Key Vault`

**Solutions**:
1. Test Key Vault connection manually
2. Check access policies and permissions
3. Verify network connectivity to Key Vault
4. Check if Key Vault name is correct

### Authentication Issues

**Error**: `Failed to get headers for tenant`

**Solutions**:
1. Check if credentials exist in Key Vault
2. Verify token cache is valid
3. Clear token cache and re-authenticate
4. Check API permissions in Azure AD

## 9. Monitoring and Logging

### Enable Key Vault Logging

1. Go to Key Vault → **Diagnostic settings**
2. Add diagnostic setting:
   - **Logs**: AuditEvent
   - **Destination**: Log Analytics workspace or Storage account

### Application Logging

The system logs Key Vault operations at INFO level:
- Credential retrieval attempts
- Token cache operations
- Migration activities
- Authentication events

### Monitoring Checklist

- [ ] Key Vault access logs enabled
- [ ] Application logging configured
- [ ] Secret expiration monitoring
- [ ] Failed authentication alerts
- [ ] Unusual access pattern detection

## 10. Next Steps

After completing Key Vault setup:

1. **Test thoroughly** in development environment
2. **Migrate existing tenants** using the migration script
3. **Deploy to production** with managed identity
4. **Monitor and maintain** the system
5. **Set up backup and disaster recovery** procedures

For customer onboarding with Key Vault, see the updated tenant onboarding documentation.
