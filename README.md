# Multi-Tenant Email → PDF → OneDrive Pipeline

> **Status:** MVP complete — ready for a manual test-run with one or more tenants.  
> Main features implemented: mail fetch, PDF text/OCR, ChatGPT analysis, routing, OneDrive upload, optional notification.
>
> **Recent Update:**
> * Email body text is now processed alongside attachments for better context and analysis accuracy. 

---

## What It Does

For every configured *tenant* the pipeline:

1. Connects to Microsoft 365 via Graph using the tenant’s `credentials.json` and `token_cache.json`.
2. Scans the inbox for unread mails with attachments.
3. For each email with attachments:
   1. **Multi-format processing:** Uses `core/file_processors/` to extract text from various file types:
       - PDFs: pdfminer → OCR fallback for scanned documents  
       - Word docs: python-docx for .docx files
       - Excel: openpyxl for .xlsx spreadsheets
       - PowerPoint: python-pptx for .pptx presentations
       - Images: pytesseract OCR for .jpg, .png, .gif, .bmp, .tiff
       - Plain text: direct reading for .txt, .csv files
   2. Reads the email body text for additional context.
   3. Sends both the email body text + attachment content to ChatGPT (`core/interpreter/chatgpt_api.py`). ChatGPT responds with:
      * `doc_type` – e.g. `invoice`, `certificate_of_analysis`, `safety_data_sheet` …
      * `summary` – human-readable summary of the document
      * `extracted_fields` – key/value pairs (supplier, amount, batch_number, …)
   3. `core/router.resolve()` computes the OneDrive target folder using
      `tenants/<name>/config.json` (tenant defaults + per-type overrides, with placeholder support).
   4. Uploads the attachment to OneDrive (`core/upload_onedrive.py`).
   5. Runs an optional extractor for structured data (if configured).
   6. Sends notification e-mails if `notification.enabled` is `true` for that `doc_type` (`core/notification.py`).

All heavy lifting is split into re-usable modules inside **`core/`**.

---

## Folder Layout

```
Mail_Auto/
├── core/                    # Shared logic
│   ├── file_processors/     # Multi-format file processors
│   │   ├── base.py          # Base processor interface
│   │   ├── factory.py       # Processor factory
│   │   ├── pdf_processor.py # PDF text extraction + OCR
│   │   ├── docx_processor.py# Word document processor
│   │   ├── xlsx_processor.py# Excel spreadsheet processor
│   │   ├── pptx_processor.py# PowerPoint processor
│   │   ├── image_processor.py# Image OCR processor
│   │   └── text_processor.py# Plain text processor
│   ├── interpreter/         # ChatGPT call wrapper
│   │   └── chatgpt_api.py   # AI analysis & classification
│   ├── mail_reader.py       # Microsoft Graph mail fetcher
│   ├── notification.py      # Graph e-mail sender
│   ├── router.py            # Folder resolution logic
│   ├── tenant_loader.py     # Enumerate tenants & configs
│   ├── unified_file_analyzer.py # Main file analysis pipeline
│   └── upload_onedrive.py   # OneDrive upload handler
├── tenants/
│   └── prototype/
│       ├── credentials.json # Azure app registration
│       ├── token_cache.json # Auto-generated after first auth
│       └── config.json      # Document type configurations
├── main.py                  # End-to-end orchestrator
├── test_multi_format.py     # Multi-format testing script
└── README.md (you are here)
```

---

## Quick Start (Manual Test)

1. **Install deps** (Python 3.10+ recommended):

   ```bash
   pip install -r requirements.txt
   ```
   *Packages used:* `requests`, `pdfminer.six`, `pdf2image`, `pytesseract`, `openai`, `python-dotenv` (if you want env-vars), etc.

2. **Prepare a tenant**
   * Copy `tenants/prototype` ➔ `tenants/<yourtenant>`.
   * Add a valid Azure app registration JSON to `credentials.json` (client-id, tenant-id, secret or cert).
   * Adjust `config.json` keywords & rules.

3. **Authenticate once** (creates `token_cache.json`):
   Run any snippet that calls `core.mail_reader.get_graph_session()` (or temporarily run the pipeline, you’ll be prompted to sign in).

4. **Run the pipeline**:
   ```bash
   python main.py --once
   ```

**Note**: This is the legacy setup method. For production use, see the Azure Key Vault setup below.

## Azure Key Vault Setup (Recommended)

For secure credential management, the system now supports Azure Key Vault integration:

### Quick Setup
1. **Configure environment**: `cp .env.example .env` and edit with your values
2. **Run setup script**: `python setup_development.py`
3. **Test the system**: `python main.py --once`

### Migration from Legacy
If you have existing credentials in JSON files:
```bash
python migrate_to_keyvault.py --backup
```

### Documentation
- [KEYVAULT_SETUP.md](KEYVAULT_SETUP.md) - Detailed Key Vault setup guide
- [AZURE_SETUP.md](AZURE_SETUP.md) - Azure AD app registration guide

## Environment & Dependencies

* Microsoft 365 tenant (Graph permissions: `Mail.Read`, `Mail.Send`, `Files.ReadWrite.All`)
* Tesseract OCR installed & on PATH for scanned PDFs (if you plan on OCR).
* OpenAI API key via `OPENAI_API_KEY` env var.

---

