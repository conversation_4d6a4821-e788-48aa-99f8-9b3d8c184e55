"""Email notification helper using Microsoft Graph API.

This module sends an email (optionally with the processed PDF attached) when
``notification.enabled`` is ``true`` for a document type in ``tenant_config``.

Public API
~~~~~~~~~~
    send_notification(doc_type, tenant_config, summary, pdf_bytes, filename, headers)
"""
from __future__ import annotations

import base64
import json
from typing import Dict, Any, List

import requests

__all__ = ["send_notification"]

_GRAPH_ENDPOINT = "https://graph.microsoft.com/v1.0/me/sendMail"


def _merge_notification_settings(tenant_config: Dict[str, Any], doc_type: str) -> Dict[str, Any]:
    """Return effective notification settings for *doc_type*.

    Precedence: per-type override → tenant-wide defaults → hard-coded fallback.
    """
    defaults: Dict[str, Any] = tenant_config.get("defaults", {}).get("notification", {})
    overrides: Dict[str, Any] = (
        tenant_config.get("document_types", {}).get(doc_type, {}).get("notification", {})
    )

    merged = {**defaults, **overrides}
    
    # Check if notification is enabled via actions.notify
    default_actions = tenant_config.get("defaults", {}).get("actions", {})
    doc_actions = tenant_config.get("document_types", {}).get(doc_type, {}).get("actions", {})
    should_notify = doc_actions.get("notify", default_actions.get("notify", False))
    
    # Ensure critical keys exist
    merged.setdefault("enabled", should_notify)
    merged.setdefault("recipients", [])
    merged.setdefault("email_template", "A new {doc_type} has arrived.")
    return merged


def _build_message(
    subject: str,
    body_text: str,
    recipients: List[Dict[str, str]],
    pdf_bytes: bytes | None,
    filename: str | None,
) -> Dict[str, Any]:
    """Build the JSON payload for Graph ``/sendMail``."""
    msg: Dict[str, Any] = {
        "subject": subject,
        "body": {"contentType": "Text", "content": body_text},
        "toRecipients": [
            {
                "emailAddress": {"name": r.get("name", ""), "address": r["email"]}
            }
            for r in recipients
        ],
    }

    if pdf_bytes and filename:
        attachment = {
            "@odata.type": "#microsoft.graph.fileAttachment",
            "name": filename,
            "contentType": "application/pdf",
            "contentBytes": base64.b64encode(pdf_bytes).decode(),
        }
        msg.setdefault("attachments", []).append(attachment)

    return {"message": msg, "saveToSentItems": "false"}


def send_notification(
    doc_type: str,
    tenant_config: Dict[str, Any],
    summary: str,
    pdf_bytes: bytes,
    filename: str,
    headers: Dict[str, str],
) -> None:
    """Send an email notification for *doc_type* if enabled in *tenant_config*.

    The function is *idempotent*: if notifications are disabled it just returns.
    """
    settings = _merge_notification_settings(tenant_config, doc_type)
    if not settings.get("enabled", False):
        return  # Notification disabled → silent no-op

    recipients = settings.get("recipients", [])
    if not recipients:
        print("⚠️  Notification enabled but no recipients configured – skipping email")
        return

    template = settings.get("email_template", "A new {doc_type} has arrived.")
    body_text = template.format(doc_type=doc_type, summary=summary or "(no summary)")
    subject = f"New {doc_type} received"

    payload = _build_message(subject, body_text, recipients, pdf_bytes, filename)

    resp = requests.post(_GRAPH_ENDPOINT, headers=headers, json=payload)
    if resp.status_code in (202, 200):
        print("📧 Notification email sent")
    else:
        try:
            err = resp.json()
        except ValueError:
            err = resp.text
        print(f"❌ Failed to send notification: {resp.status_code} {err}")
