{"AccessToken": {"********-0000-0000-d797-e49667a6388c.9188040d-6c67-4c5b-b112-36a304b66dad-login.microsoftonline.com-accesstoken-587be001-bcd2-4275-b59b-5011141bd8d0-consumers-files.readwrite files.readwrite.all mail.read mail.readwrite mail.send openid profile": {"credential_type": "AccessToken", "secret": "EwBYBMl6BAAUBKgm8k1UswUNwklmy2v7U/S+1fEAAZ5WyxHWAOpjTSL5OXyQwAvBvJZMCA6wOYbgEQblkjh1rhSXWCSGgNqJZc2lGrz9PSIezCTGQQCiaTFb5X/Ie2dD5BryU4CNshFWKrpwigR80mGpE1hriUe+LxuvgLyNEpXxdVWtK9DoNTVTWbmdaOb7pCqSlvNWlQ93pUEHYA2R1qVEhEWUjbY9bgeCzIJFllN579tBz51DptHhQHFg30pVGE9y0wrfPyuEv8XY9+7cpq417h/TonLjxv6BMOAUYFH+mTFaJkqM5KRjjW0/E9O+cftscVmBobqihfbpU9LxDVxG/mQdpvLY9pml/VtXmKrCV7/gbja8Rk8ya1SO8gYQZgAAEIfsV19jRqtdwIAitYwW80UgA5aPitGIrvr/aoY/P8Buf53oc9aRe0Un7i4jYToKGOaJZ6z1njjhWCUk5zHU28oYymRllENl25jk5B0cHf9z1v4ISkv/zeoo08glBx3Jkc/sgvMgLwNMQLwPhp7YcMyuhw6XhsrHJEr0g3coxICZJoXR3a89t9N4YjjC9xgrm6+E2DRNMLeEvQcHL26gwXiiAXyn0SRHzwl29rNXPfLS7Y3CczwVQqwYDTmyLh6d9iEl+zZYzl59TB2Ca1lLeIJXQh2Kzp+CR9Mih0SBVPgIU3KXSC7l2tWMDeXD8Yh25iHWYKZLZ07qeDjYZoyfpQcWrbsyVrv1xH/V3xOMyAGKQBSA5R0yYWePVIAB71U6LRoXLKr/i10cA3ZkwgSmukgXBroWNpRuNeF55LC2YV76JuvwNMbQfW9sgNaYpOh2oO8nGWfqBk6h2IUn44ONnPAaJ3hTpTtgXtiyCDPb316cdNHb0wtjd6WRbMg4hR5D3y9i1QlmAtoyNvUZBpxw+hqaHqSPd8SHb+k0EePBF3r3iu7cEN0WbdnsmR069bn1rpyZj1kxIQfwNN41KmliO3nNyFJu+LaJCwHELY7In2J27g3OpHK+6UB15tf2u+r4bne1SNoSRivrc9VMM/GWAP9QgTVbcud/ALMrfcyvwagjOh1Fsmv+pDdGWaRJTgA68kT0P9DMCc56lfounn6PP57NBihfmlmKcD8nVQmELTnhXWnB60jHohN+L5BRsk3XSa/djk05vRCB8gqFJ2D+lr6FEf6PztViXjx1rsN7aPZ8zueS1LjZ3czqdJTOQqgEQRu1I5h/1i1BetnNT6aINwlRZ7kx2Pnlrs4dJHe6D2VCvkw3mzG+yY+RX6AhSlYEVDQt88Q201zTB+t7NZOG8O+xhYvM7OkKEtY103B4NSl/s03+BIaH9nPa9Ncgq7KXwAbuxjFtbadrdljvo9IvXHDjR+dOsfMzjG0m8H8uDP/M815Xbat9uW+FJZIFQqj2GHeIb9ag3WVP1dh8bcZaeuvgpEL/LTi0QsZkSNKL/eV+orMNmmjvGOThxKiLu3PkqPueWgM=", "home_account_id": "********-0000-0000-d797-e49667a6388c.9188040d-6c67-4c5b-b112-36a304b66dad", "environment": "login.microsoftonline.com", "client_id": "587be001-bcd2-4275-b59b-5011141bd8d0", "target": "Files.ReadWrite Files.ReadWrite.All Mail.Read Mail.ReadWrite Mail.Send openid profile", "realm": "consumers", "token_type": "Bearer", "cached_at": "**********", "expires_on": "**********", "extended_expires_on": "**********"}}, "Account": {"********-0000-0000-d797-e49667a6388c.9188040d-6c67-4c5b-b112-36a304b66dad-login.microsoftonline.com-consumers": {"home_account_id": "********-0000-0000-d797-e49667a6388c.9188040d-6c67-4c5b-b112-36a304b66dad", "environment": "login.microsoftonline.com", "realm": "consumers", "local_account_id": "********-0000-0000-d797-e49667a6388c", "username": "wati<PERSON><PERSON><EMAIL>", "authority_type": "MSSTS", "account_source": "urn:ietf:params:oauth:grant-type:device_code"}}, "IdToken": {"********-0000-0000-d797-e49667a6388c.9188040d-6c67-4c5b-b112-36a304b66dad-login.microsoftonline.com-idtoken-587be001-bcd2-4275-b59b-5011141bd8d0-consumers-": {"credential_type": "IdToken", "secret": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjRweGhwaHJGam9vQlhWNnQwSk9fa3BUUU5sOCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VT7I6N8-JrYAEV3P9YCCBiB-IuKLPXjR2fyd9MndaAqZLSPEv8T_-drcuqskmkhV8T0JEGyjuCc6R4E5DwoB7hOyfIIOr2xsNSHC1WkulLLFbmKiuYmluBDiRhSsYnPRXk8rH_cWHpxZyi4r2qc2VtDKV3kCBTPdej1iIkE1Q1jQmegtEIgrpvqtcU8DT-q4dopFxbqokP05aitE9vb07sj-xDCG1fLjprpF05W8YdNxYhDx0RBl-G_ETtmtyI9vVwiW5BXTfxFR-uW0MFmihWhtncCLgHD4TfZh520OfKEho-gpPxpwXSPbNR-1F74gCdbjlZnsVO9atg4lFd_u3Q", "home_account_id": "********-0000-0000-d797-e49667a6388c.9188040d-6c67-4c5b-b112-36a304b66dad", "environment": "login.microsoftonline.com", "realm": "consumers", "client_id": "587be001-bcd2-4275-b59b-5011141bd8d0"}}, "RefreshToken": {"********-0000-0000-d797-e49667a6388c.9188040d-6c67-4c5b-b112-36a304b66dad-login.microsoftonline.com-refreshtoken-587be001-bcd2-4275-b59b-5011141bd8d0--files.readwrite files.readwrite.all mail.read mail.readwrite mail.send openid profile": {"credential_type": "RefreshToken", "secret": "M.C505_BAY.0.U.-Cqu9H04Kv!mFbbNFxyXo3wpW3AmRgwQesoe3ZeRc89QDmRE2n7YlYUmn5o7Nn7KHGq6BpMzgNsvsvRKDr*OzdkHO9bd*pCwtMEM8REA1TOQsZ*YnRxFVX2Pw!oKEAUhUE4Zl2yplJI6uUTuL62VQXQieiSwNr2vuVJ1bZ94X1Omzst9jGv6qpqaRXAxcO*5woOMflENT3xELFYzZsfNxLM1cmErZguMV6IstsQIeO5cJ4Hi7GaHuSAJAlM2DLRQPJUH4sqKC1zLSDL7WDhVOu*kmylPKamUmVdNO*EPwxtXmlHjei890Hvx3vILvLEV7v9T1Zk97dO6XTHqUmZGhz7TYvF1tZiRsfbyz8U946luAypGyyqcD0yKC8RSxWJ!KKQ$$", "home_account_id": "********-0000-0000-d797-e49667a6388c.9188040d-6c67-4c5b-b112-36a304b66dad", "environment": "login.microsoftonline.com", "client_id": "587be001-bcd2-4275-b59b-5011141bd8d0", "target": "Files.ReadWrite Files.ReadWrite.All Mail.Read Mail.ReadWrite Mail.Send openid profile", "last_modification_time": "**********"}}, "AppMetadata": {"appmetadata-login.microsoftonline.com-587be001-bcd2-4275-b59b-5011141bd8d0": {"client_id": "587be001-bcd2-4275-b59b-5011141bd8d0", "environment": "login.microsoftonline.com"}}}