#!/usr/bin/env python3
"""
Development environment setup script for Mail Auto with Azure Key Vault.
This script helps configure the development environment for testing.
"""

import os
import sys
import logging
from typing import Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_env_file() -> bool:
    """Check if .env file exists and has required variables."""
    if not os.path.exists('.env'):
        logger.error("❌ .env file not found. Please copy .env.example to .env and configure it.")
        return False
    
    required_vars = [
        'MAIL_AUTO_ENVIRONMENT',
        'DEV_KEY_VAULT_URL',
        'DEV_TENANT_ID',
        'DEV_CLIENT_ID',
        'DEV_CLIENT_SECRET'
    ]
    
    # Load .env file
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        logger.error("❌ python-dotenv not installed. Run: pip install python-dotenv")
        return False
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        logger.info("Please configure these in your .env file")
        return False
    
    logger.info("✅ .env file configured correctly")
    return True


def check_dependencies() -> bool:
    """Check if required Python packages are installed."""
    required_packages = [
        'azure-keyvault-secrets',
        'azure-identity',
        'python-dotenv',
        'msal',
        'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"❌ Missing required packages: {', '.join(missing_packages)}")
        logger.info("Run: pip install -r requirements.txt")
        return False
    
    logger.info("✅ All required packages installed")
    return True


def test_key_vault_connection() -> bool:
    """Test connection to Azure Key Vault."""
    try:
        from core.config import config_manager
        from core.key_vault_service import key_vault_service
        
        logger.info(f"Testing Key Vault connection...")
        logger.info(f"Environment: {config_manager.current_environment}")
        logger.info(f"Key Vault URL: {config_manager.config.key_vault.vault_url}")
        
        # Try to list secrets (this will test authentication)
        tenants = key_vault_service.list_tenant_secrets()
        logger.info(f"✅ Key Vault connection successful. Found {len(tenants)} tenant(s)")
        
        if tenants:
            logger.info(f"Existing tenants: {', '.join(tenants)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Key Vault connection failed: {e}")
        logger.info("Please check your Key Vault configuration and access policies")
        return False


def check_azure_permissions() -> bool:
    """Check if Azure app registration has required permissions."""
    try:
        from core.config import config_manager
        
        logger.info("Checking Azure app registration permissions...")
        
        # This is a basic check - in a real scenario you might want to test actual API calls
        config = config_manager.config
        
        if not config.key_vault.client_id:
            logger.error("❌ Client ID not configured")
            return False
        
        if not config.key_vault.client_secret and not config.key_vault.use_managed_identity:
            logger.error("❌ Client secret not configured and managed identity not enabled")
            return False
        
        logger.info("✅ Azure app registration appears to be configured")
        logger.info("Note: You should verify API permissions in Azure Portal")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to check Azure configuration: {e}")
        return False


def setup_development_tenant() -> bool:
    """Set up a development tenant for testing."""
    try:
        from core.tenant_loader import get_tenant_credentials
        
        # Check if prototype tenant exists
        prototype_creds = get_tenant_credentials("prototype")
        
        if prototype_creds:
            logger.info("✅ Prototype tenant already configured")
            return True
        
        # Check if there's a local prototype tenant to migrate
        prototype_path = "tenants/prototype/credentials.json"
        if os.path.exists(prototype_path):
            logger.info("Found local prototype tenant. Consider running migration:")
            logger.info("python migrate_to_keyvault.py --tenant prototype --backup")
            return True
        
        logger.warning("⚠️ No prototype tenant found")
        logger.info("You can:")
        logger.info("1. Create tenants/prototype/credentials.json manually")
        logger.info("2. Use the tenant onboarding flow")
        logger.info("3. Migrate existing tenants with migrate_to_keyvault.py")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to check development tenant: {e}")
        return False


def main():
    """Main setup function."""
    logger.info("🚀 Setting up Mail Auto development environment with Azure Key Vault")
    logger.info("=" * 60)
    
    checks = [
        ("Environment file", check_env_file),
        ("Python dependencies", check_dependencies),
        ("Key Vault connection", test_key_vault_connection),
        ("Azure permissions", check_azure_permissions),
        ("Development tenant", setup_development_tenant),
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        logger.info(f"\n📋 Checking {check_name}...")
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            logger.error(f"❌ {check_name} check failed with error: {e}")
            all_passed = False
    
    logger.info("\n" + "=" * 60)
    
    if all_passed:
        logger.info("🎉 Development environment setup completed successfully!")
        logger.info("\nNext steps:")
        logger.info("1. Test the system: python main.py --once")
        logger.info("2. Check the logs for any authentication prompts")
        logger.info("3. Verify emails are processed correctly")
        logger.info("\nFor production setup, see KEYVAULT_SETUP.md")
    else:
        logger.error("❌ Setup incomplete. Please fix the issues above and run again.")
        logger.info("\nFor help, see:")
        logger.info("- KEYVAULT_SETUP.md for detailed setup instructions")
        logger.info("- AZURE_SETUP.md for Azure configuration")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
